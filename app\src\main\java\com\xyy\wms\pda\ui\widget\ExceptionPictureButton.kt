package com.xyy.wms.pda.ui.widget

import android.content.Context
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatButton
import com.xyy.wms.pda.bean.instorage.BusinessInfo
import com.xyy.wms.pda.config.ExceptionPictureConfig

/**
 * 异常图片按钮组件
 * 用于在业务表单中集成异常图片上传功能
 */
class ExceptionPictureButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AppCompatButton(context, attrs, defStyleAttr) {
    
    // 组件属性
    var maxCount: Int = ExceptionPictureConfig.MAX_PICTURE_COUNT  // 最大图片数量
    var currentCount: Int = 0                                     // 当前图片数量
        set(value) {
            field = value
            updateButtonText()
        }
    var businessInfo: BusinessInfo? = null                        // 业务信息
    var onPictureUploadListener: (() -> Unit)? = null            // 上传完成回调
    
    init {
        updateButtonText()
        setOnClickListener {
            showPictureDialog()
        }
    }
    
    /**
     * 更新按钮文本
     */
    private fun updateButtonText() {
        text = if (currentCount > 0) "异常图片($currentCount)" else "异常图片"
    }
    
    /**
     * 显示图片上传弹窗
     */
    private fun showPictureDialog() {
        if (businessInfo == null) {
            // 提示需要先填写业务信息
            return
        }

        // 这里应该通过回调让Activity来处理弹窗显示
        onPictureUploadListener?.invoke()
    }
    
    /**
     * 设置业务信息
     */
    fun setBusinessInfo(info: BusinessInfo) {
        this.businessInfo = info
    }
    
    /**
     * 获取当前图片数量
     */
    fun getPictureCount(): Int = currentCount
}
